package com.xiaozhi.controller;

import com.xiaozhi.common.interceptor.PassAuth;
import com.xiaozhi.common.web.BizError;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.dto.McpEndpointDto;
import com.xiaozhi.entity.SysMcpEndpoint;
import com.xiaozhi.service.SysMcpEndpointService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.vavr.control.Either;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

/**
 * MCP Endpoint Controller for managing MCP endpoints and retrieving available tools
 */
@Tag(name = "McpEndpoint", description = "MCP端点管理")
@RestController
@RequestMapping("mcp-endpoints")
public class McpEndpointController {

    @Resource
    private SysMcpEndpointService mcpEndpointService;

    // ==================== CRUD Operations ====================

    @Operation(summary = "获取MCP端点列表", description = "分页查询MCP端点列表")
    @GetMapping
    public Resp list(@RequestParam(defaultValue = "1") int pageNum,
                     @RequestParam(defaultValue = "10") int pageSize,
                     @RequestParam(required = false) String name,
                     @RequestParam(required = false) Boolean enabled) {
        return mcpEndpointService.findPage(pageNum, pageSize, name, enabled);
    }

    @Operation(summary = "获取MCP端点详情", description = "根据ID获取MCP端点详情")
    @GetMapping("/{id}")
    public Resp getById(@PathVariable Integer id) {
        SysMcpEndpoint endpoint = mcpEndpointService.getById(id);
        if (endpoint == null) {
            return Resp.fail(404, "端点不存在");
        }
        return Resp.succeed(endpoint);
    }

    @PassAuth
    @PostMapping
    @Operation(summary = "添加MCP端点", description = "添加新的MCP端点")
    public Resp add(@RequestBody McpEndpointDto dto) {
        return mcpEndpointService.addEndpoint(dto);
    }

    @Operation(summary = "更新MCP端点", description = "更新指定的MCP端点")
    @PutMapping("/{id}")
    public Resp update(@PathVariable Integer id, @RequestBody McpEndpointDto dto) {
        return mcpEndpointService.updateEndpoint(id, dto);
    }

    @Operation(summary = "删除MCP端点", description = "删除指定的MCP端点")
    @DeleteMapping("/{id}")
    public Resp delete(@PathVariable Integer id) {
        return mcpEndpointService.deleteEndpoint(id);
    }

    @Operation(summary = "切换端点状态", description = "启用或禁用MCP端点")
    @PutMapping("/{id}/status")
    public Resp toggleStatus(@PathVariable Integer id, @RequestParam Boolean enabled) {
        return mcpEndpointService.toggleEndpointStatus(id, enabled);
    }

    // ==================== Tool Operations ====================

    @PassAuth
    @Operation(summary = "获取MCP端点可用工具", description = "传入MCP URL，返回该端点的可用工具列表")
    @PostMapping("/tools")
    public Either<BizError, ?> getAvailableTools(@RequestParam String mcpUrl, @RequestParam String mcpToken) {
        return mcpEndpointService.getAvailableTools(mcpUrl, mcpToken);
    }

    @PassAuth
    @Operation(summary = "测试MCP端点连接", description = "测试指定MCP端点的连接状态")
    @PostMapping("/test-connection")
    public Resp testConnection(@RequestParam String url, @RequestParam(required = false) String authToken) {
        return mcpEndpointService.testEndpointConnection(url, authToken);
    }

    @Operation(summary = "获取已保存端点的工具", description = "获取已保存的MCP端点的可用工具")
    @GetMapping("/{id}/tools")
    public Either<BizError, ?> getEndpointTools(@PathVariable Integer id) {
        SysMcpEndpoint endpoint = mcpEndpointService.getById(id);
        return mcpEndpointService.getAvailableTools(endpoint.getUrl(), endpoint.getToken());
    }
}

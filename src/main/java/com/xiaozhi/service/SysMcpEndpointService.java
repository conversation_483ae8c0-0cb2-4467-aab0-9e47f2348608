package com.xiaozhi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xiaozhi.common.web.BizError;
import com.xiaozhi.entity.SysMcpEndpoint;
import com.xiaozhi.common.web.Resp;
import com.xiaozhi.dto.McpEndpointDto;
import io.vavr.control.Either;

import java.util.List;

/**
 * MCP endpoint service interface
 */
public interface SysMcpEndpointService extends IService<SysMcpEndpoint> {
    
    /**
     * 获取启用的端点列表
     */
    List<SysMcpEndpoint> getEnabledEndpoints();
    
    /**
     * 分页查询端点列表
     */
    Resp findPage(int pageNum, int pageSize, String name, Boolean enabled);
    
    /**
     * 根据ID获取端点详情
     */
    SysMcpEndpoint getById(Integer id);

    /**
     * 添加端点
     */
    Resp addEndpoint(McpEndpointDto dto);

    /**
     * 更新端点
     */
    Resp updateEndpoint(Integer id, McpEndpointDto dto);

    /**
     * 删除端点（软删除）
     */
    Resp deleteEndpoint(Integer id);

    /**
     * 切换端点启用状态
     */
    Resp toggleEndpointStatus(Integer id, Boolean enabled);
    
    /**
     * 测试端点连接
     */
    Resp testEndpointConnection(String url, String authToken);

    /**
     * 获取MCP端点可用工具
     */
    Either<BizError, ?> getAvailableTools(String mcpUrl, String mcpToken);
}

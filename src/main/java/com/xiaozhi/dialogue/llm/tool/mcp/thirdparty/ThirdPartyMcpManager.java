package com.xiaozhi.dialogue.llm.tool.mcp.thirdparty;

import com.fasterxml.jackson.core.type.TypeReference;
import com.xiaozhi.entity.SysMcpEndpoint;
import com.xiaozhi.service.SysMcpEndpointService;
import com.xiaozhi.utils.JsonUtil;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.tool.function.FunctionToolCallback;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Third-party MCP Manager
 * 管理第三方MCP端点的连接、断开和工具获取
 */
@Component
public class ThirdPartyMcpManager {
    private static final Logger logger = LoggerFactory.getLogger(ThirdPartyMcpManager.class);
    
    @Resource
    private SysMcpEndpointService mcpEndpointService;
    
    @Resource
    private ThirdPartyMcpService thirdPartyMcpService;
    
    // 缓存已连接的端点和其工具
    private final Map<String, List<FunctionToolCallback>> endpointTools = new ConcurrentHashMap<>();
    
    /**
     * 连接到指定的MCP端点并获取工具
     *
     * @param endpointUrl MCP端点URL
     * @param headers 请求头
     * @return 是否连接成功
     */
    public boolean connectToEndpoint(String endpointUrl, Map<String, String> headers) {
        try {
            logger.debug("正在连接到MCP端点: {}", endpointUrl);
            
            List<FunctionToolCallback> tools = thirdPartyMcpService.connectAndRegisterTools(endpointUrl, headers);
            
            if (!tools.isEmpty()) {
                endpointTools.put(endpointUrl, tools);
                logger.info("成功连接到MCP端点: {}, 获取到 {} 个工具", endpointUrl, tools.size());
                return true;
            } else {
                logger.warn("连接到MCP端点成功但未获取到工具: {}", endpointUrl);
                return false;
            }
        } catch (Exception e) {
            logger.error("连接到MCP端点失败: {}", endpointUrl, e);
            return false;
        }
    }
    
    /**
     * 从指定端点断开连接
     *
     * @param endpointUrl MCP端点URL
     * @return 是否断开成功
     */
    public boolean disconnectFromEndpoint(String endpointUrl) {
        try {
            boolean disconnected = thirdPartyMcpService.disconnectFromEndpoint(endpointUrl);
            endpointTools.remove(endpointUrl);
            
            if (disconnected) {
                logger.info("成功断开MCP端点连接: {}", endpointUrl);
            }
            
            return disconnected;
        } catch (Exception e) {
            logger.error("断开MCP端点连接失败: {}", endpointUrl, e);
            return false;
        }
    }
    
    /**
     * 检查是否已连接到指定端点
     *
     * @param endpointUrl MCP端点URL
     * @return 是否已连接
     */
    public boolean isConnectedToEndpoint(String endpointUrl) {
        return thirdPartyMcpService.isConnectedToEndpoint(endpointUrl) && endpointTools.containsKey(endpointUrl);
    }
    
    /**
     * 获取指定端点的工具列表
     *
     * @param endpointUrl MCP端点URL
     * @return 工具列表
     */
    public List<FunctionToolCallback> getToolsFromEndpoint(String endpointUrl) {
        return endpointTools.getOrDefault(endpointUrl, Collections.emptyList());
    }
    
    /**
     * 获取所有已连接端点的工具
     *
     * @return 所有工具的列表
     */
    public List<FunctionToolCallback> getAllTools() {
        List<FunctionToolCallback> allTools = new ArrayList<>();
        endpointTools.values().forEach(allTools::addAll);
        return allTools;
    }
    
    /**
     * 初始化所有启用的MCP端点
     * 从数据库中查询启用的端点并尝试连接
     *
     * @return 成功连接的端点数量
     */
    public int initializeEnabledEndpoints() {
        logger.info("开始初始化启用的MCP端点...");
        
        List<SysMcpEndpoint> enabledEndpoints = mcpEndpointService.getEnabledEndpoints();
        int successCount = 0;
        
        for (SysMcpEndpoint endpoint : enabledEndpoints) {
            try {
                Map<String, String> headers = new HashMap<>();
                
                // 添加认证token
                if (endpoint.getToken() != null && !endpoint.getToken().trim().isEmpty()) {
                    headers.put("Authorization", "Bearer " + endpoint.getToken());
                }
                
                // 解析其他headers
                if (endpoint.getHeaders() != null && !endpoint.getHeaders().trim().isEmpty()) {
                    try {
                        var additionalHeaders = JsonUtil.fromJson(endpoint.getHeaders(), new TypeReference<Map<String, String>>() {});
                        headers.putAll(additionalHeaders);
                    } catch (Exception e) {
                        logger.warn("解析端点headers失败: {} - {}", endpoint.getUrl(), e.getMessage());
                    }
                }
                
                boolean connected = connectToEndpoint(endpoint.getUrl(), headers);
                if (connected) {
                    successCount++;
                }
                
            } catch (Exception e) {
                logger.error("初始化MCP端点失败: {} - {}", endpoint.getUrl(), e.getMessage());
            }
        }
        
        logger.info("MCP端点初始化完成，成功连接 {}/{} 个端点", successCount, enabledEndpoints.size());
        return successCount;
    }
    
    /**
     * 获取所有已连接的端点URL
     *
     * @return 端点URL集合
     */
    public Set<String> getConnectedEndpoints() {
        return new HashSet<>(endpointTools.keySet());
    }
    
    /**
     * 清理所有连接
     */
    public void cleanup() {
        logger.info("清理所有MCP端点连接...");
        
        Set<String> endpoints = new HashSet<>(endpointTools.keySet());
        for (String endpoint : endpoints) {
            disconnectFromEndpoint(endpoint);
        }
        
        endpointTools.clear();
        logger.info("MCP端点连接清理完成");
    }
    
    /**
     * 重新加载所有端点
     * 先清理现有连接，然后重新初始化
     *
     * @return 成功连接的端点数量
     */
    public int reloadEndpoints() {
        logger.info("重新加载MCP端点...");
        cleanup();
        return initializeEnabledEndpoints();
    }
}

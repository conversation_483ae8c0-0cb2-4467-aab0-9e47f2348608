package com.xiaozhi.dialogue.llm.tool.mcp;

import com.xiaozhi.communication.common.ChatSession;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * MCP工具使用验证器
 * 用于验证MCP工具是否被正确注册和使用
 */
@Component
public class McpToolUsageVerifier {
    private static final Logger logger = LoggerFactory.getLogger(McpToolUsageVerifier.class);

    @Resource
    private McpToolRegistrationService mcpToolRegistrationService;

    /**
     * 验证会话中的MCP工具注册情况
     *
     * @param chatSession 聊天会话
     * @return 验证结果
     */
    public McpToolVerificationResult verifyMcpToolsRegistration(ChatSession chatSession) {
        logger.info("开始验证会话 {} 的MCP工具注册情况", chatSession.getSessionId());

        try {
            // 获取所有已注册的工具
            List<ToolCallback> allTools = chatSession.getToolCallbacks();
            
            // 筛选MCP工具（名称以"mcp_"开头）
            List<ToolCallback> mcpTools = allTools.stream()
                    .filter(tool -> tool.getToolDefinition().name().startsWith("mcp_"))
                    .collect(Collectors.toList());

            // 获取统计信息
            McpToolRegistrationService.McpToolStats stats = mcpToolRegistrationService.getMcpToolStats(chatSession);

            // 创建验证结果
            McpToolVerificationResult result = new McpToolVerificationResult(
                    chatSession.getSessionId(),
                    allTools.size(),
                    mcpTools.size(),
                    stats.getThirdPartyMcpTools(),
                    mcpTools.stream()
                            .map(tool -> tool.getToolDefinition().name())
                            .collect(Collectors.toList()),
                    true,
                    null
            );

            logger.info("MCP工具验证完成 - SessionId: {}, 总工具数: {}, MCP工具数: {}, 第三方MCP工具数: {}",
                    chatSession.getSessionId(), result.getTotalTools(), result.getMcpTools(), result.getThirdPartyMcpTools());

            // 输出MCP工具列表
            if (!result.getMcpToolNames().isEmpty()) {
                logger.info("已注册的MCP工具: {}", String.join(", ", result.getMcpToolNames()));
            } else {
                logger.warn("警告: 没有发现任何MCP工具！");
            }

            return result;

        } catch (Exception e) {
            logger.error("验证MCP工具注册时发生错误 - SessionId: {}", chatSession.getSessionId(), e);
            return new McpToolVerificationResult(
                    chatSession.getSessionId(),
                    0, 0, 0,
                    List.of(),
                    false,
                    e.getMessage()
            );
        }
    }

    /**
     * 验证MCP工具是否可以被LLM使用
     *
     * @param chatSession 聊天会话
     * @return 是否可用
     */
    public boolean verifyMcpToolsAvailableForLLM(ChatSession chatSession) {
        logger.info("验证MCP工具是否可被LLM使用 - SessionId: {}", chatSession.getSessionId());

        try {
            // 检查会话是否有工具回调
            List<ToolCallback> toolCallbacks = chatSession.getToolCallbacks();
            if (toolCallbacks.isEmpty()) {
                logger.warn("会话中没有任何工具回调 - SessionId: {}", chatSession.getSessionId());
                return false;
            }

            // 检查是否有MCP工具
            long mcpToolCount = toolCallbacks.stream()
                    .filter(tool -> tool.getToolDefinition().name().startsWith("mcp_"))
                    .count();

            if (mcpToolCount == 0) {
                logger.warn("会话中没有MCP工具 - SessionId: {}", chatSession.getSessionId());
                return false;
            }

            logger.info("MCP工具可用性验证通过 - SessionId: {}, MCP工具数: {}", 
                    chatSession.getSessionId(), mcpToolCount);
            return true;

        } catch (Exception e) {
            logger.error("验证MCP工具可用性时发生错误 - SessionId: {}", chatSession.getSessionId(), e);
            return false;
        }
    }

    /**
     * 输出详细的工具信息用于调试
     *
     * @param chatSession 聊天会话
     */
    public void debugToolInformation(ChatSession chatSession) {
        logger.info("=== 调试工具信息 - SessionId: {} ===", chatSession.getSessionId());

        try {
            List<ToolCallback> allTools = chatSession.getToolCallbacks();
            logger.info("总工具数: {}", allTools.size());

            // 按类型分组显示工具
            allTools.stream()
                    .collect(Collectors.groupingBy(tool -> {
                        String name = tool.getToolDefinition().name();
                        if (name.startsWith("mcp_")) {
                            return "MCP工具";
                        } else if (name.startsWith("func_")) {
                            return "功能工具";
                        } else {
                            return "其他工具";
                        }
                    }))
                    .forEach((category, tools) -> {
                        logger.info("{} ({}个):", category, tools.size());
                        tools.forEach(tool -> {
                            String name = tool.getToolDefinition().name();
                            String description = tool.getToolDefinition().description();
                            logger.info("  - {}: {}", name, description != null ? description : "无描述");
                        });
                    });

        } catch (Exception e) {
            logger.error("输出调试工具信息时发生错误", e);
        }

        logger.info("=== 调试工具信息结束 ===");
    }

    /**
     * MCP工具验证结果
     */
    public static class McpToolVerificationResult {
        private final String sessionId;
        private final int totalTools;
        private final int mcpTools;
        private final int thirdPartyMcpTools;
        private final List<String> mcpToolNames;
        private final boolean success;
        private final String errorMessage;

        public McpToolVerificationResult(String sessionId, int totalTools, int mcpTools, 
                                       int thirdPartyMcpTools, List<String> mcpToolNames, 
                                       boolean success, String errorMessage) {
            this.sessionId = sessionId;
            this.totalTools = totalTools;
            this.mcpTools = mcpTools;
            this.thirdPartyMcpTools = thirdPartyMcpTools;
            this.mcpToolNames = mcpToolNames;
            this.success = success;
            this.errorMessage = errorMessage;
        }

        // Getters
        public String getSessionId() { return sessionId; }
        public int getTotalTools() { return totalTools; }
        public int getMcpTools() { return mcpTools; }
        public int getThirdPartyMcpTools() { return thirdPartyMcpTools; }
        public List<String> getMcpToolNames() { return mcpToolNames; }
        public boolean isSuccess() { return success; }
        public String getErrorMessage() { return errorMessage; }

        @Override
        public String toString() {
            return String.format("McpToolVerificationResult{sessionId='%s', totalTools=%d, mcpTools=%d, thirdPartyMcpTools=%d, success=%s, mcpToolNames=%s}",
                    sessionId, totalTools, mcpTools, thirdPartyMcpTools, success, mcpToolNames);
        }
    }
}

package com.xiaozhi.dialogue.llm.tool.mcp.demo;

import com.xiaozhi.communication.common.ChatSession;
import com.xiaozhi.dialogue.llm.tool.mcp.McpToolRegistrationService;
import com.xiaozhi.dialogue.llm.tool.mcp.thirdparty.ThirdPartyMcpManager;
import com.xiaozhi.entity.SysMcpEndpoint;
import com.xiaozhi.service.SysMcpEndpointService;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * MCP工具注册演示类
 * 展示如何使用MCP工具注册功能
 */
@Component
public class McpToolRegistrationDemo {
    private static final Logger logger = LoggerFactory.getLogger(McpToolRegistrationDemo.class);

    @Resource
    private McpToolRegistrationService mcpToolRegistrationService;

    @Resource
    private ThirdPartyMcpManager thirdPartyMcpManager;

    @Resource
    private SysMcpEndpointService mcpEndpointService;

    /**
     * 演示完整的MCP工具注册流程
     *
     * @param chatSession 聊天会话
     */
    public void demonstrateFullWorkflow(ChatSession chatSession) {
        logger.info("=== MCP工具注册演示开始 ===");
        
        // 1. 显示当前数据库中的MCP端点配置
        showMcpEndpointConfiguration();
        
        // 2. 初始化所有MCP工具
        logger.info("步骤2: 初始化所有MCP工具");
        mcpToolRegistrationService.initializeAllMcpTools(chatSession);
        
        // 3. 显示工具注册统计信息
        showToolStatistics(chatSession);
        
        // 4. 列出所有已注册的工具
        listRegisteredTools(chatSession);
        
        // 5. 演示第三方MCP管理功能
        demonstrateThirdPartyMcpManagement();
        
        logger.info("=== MCP工具注册演示结束 ===");
    }

    /**
     * 显示数据库中的MCP端点配置
     */
    private void showMcpEndpointConfiguration() {
        logger.info("步骤1: 显示数据库中的MCP端点配置");
        
        try {
            List<SysMcpEndpoint> enabledEndpoints = mcpEndpointService.getEnabledEndpoints();
            
            if (enabledEndpoints.isEmpty()) {
                logger.info("  - 没有找到启用的MCP端点");
                logger.info("  - 建议: 在数据库中添加MCP端点配置");
            } else {
                logger.info("  - 找到 {} 个启用的MCP端点:", enabledEndpoints.size());
                for (SysMcpEndpoint endpoint : enabledEndpoints) {
                    logger.info("    * 名称: {}, URL: {}, 认证: {}", 
                        endpoint.getName(), 
                        endpoint.getUrl(),
                        endpoint.getToken() != null ? "已配置" : "未配置"
                    );
                }
            }
        } catch (Exception e) {
            logger.error("获取MCP端点配置失败", e);
        }
    }

    /**
     * 显示工具注册统计信息
     *
     * @param chatSession 聊天会话
     */
    private void showToolStatistics(ChatSession chatSession) {
        logger.info("步骤3: 显示工具注册统计信息");
        
        try {
            McpToolRegistrationService.McpToolStats stats = 
                mcpToolRegistrationService.getMcpToolStats(chatSession);
            
            logger.info("  - 工具统计信息: {}", stats.toString());
            logger.info("    * 总工具数: {}", stats.getTotalTools());
            logger.info("    * MCP工具数: {}", stats.getMcpTools());
            logger.info("    * 第三方MCP工具数: {}", stats.getThirdPartyMcpTools());
            
            if (stats.getMcpTools() == 0) {
                logger.warn("  - 警告: 没有注册任何MCP工具");
            } else {
                logger.info("  - 成功: MCP工具注册正常");
            }
        } catch (Exception e) {
            logger.error("获取工具统计信息失败", e);
        }
    }

    /**
     * 列出所有已注册的工具
     *
     * @param chatSession 聊天会话
     */
    private void listRegisteredTools(ChatSession chatSession) {
        logger.info("步骤4: 列出所有已注册的工具");
        
        try {
            List<ToolCallback> allTools = chatSession.getToolCallbacks();
            
            if (allTools.isEmpty()) {
                logger.info("  - 没有注册任何工具");
            } else {
                logger.info("  - 已注册 {} 个工具:", allTools.size());
                
                // 分类显示工具
                int mcpToolCount = 0;
                int regularToolCount = 0;
                
                for (ToolCallback tool : allTools) {
                    String toolName = tool.getToolDefinition().name();
                    if (toolName.startsWith("mcp_")) {
                        if (mcpToolCount == 0) {
                            logger.info("    MCP工具:");
                        }
                        logger.info("      * {}", toolName);
                        mcpToolCount++;
                    } else {
                        if (regularToolCount == 0 && mcpToolCount > 0) {
                            logger.info("    常规工具:");
                        }
                        if (regularToolCount < 5) { // 只显示前5个常规工具
                            logger.info("      * {}", toolName);
                        }
                        regularToolCount++;
                    }
                }
                
                if (regularToolCount > 5) {
                    logger.info("      * ... 还有 {} 个常规工具", regularToolCount - 5);
                }
            }
        } catch (Exception e) {
            logger.error("列出已注册工具失败", e);
        }
    }

    /**
     * 演示第三方MCP管理功能
     */
    private void demonstrateThirdPartyMcpManagement() {
        logger.info("步骤5: 演示第三方MCP管理功能");
        
        try {
            // 显示已连接的端点
            var connectedEndpoints = thirdPartyMcpManager.getConnectedEndpoints();
            logger.info("  - 已连接的端点数量: {}", connectedEndpoints.size());
            
            if (!connectedEndpoints.isEmpty()) {
                logger.info("  - 已连接的端点:");
                for (String endpoint : connectedEndpoints) {
                    logger.info("    * {}", endpoint);
                }
            }
            
            // 显示可用的第三方工具
            var thirdPartyTools = thirdPartyMcpManager.getAllTools();
            logger.info("  - 第三方MCP工具数量: {}", thirdPartyTools.size());
            
            if (!thirdPartyTools.isEmpty()) {
                logger.info("  - 第三方MCP工具列表:");
                for (var tool : thirdPartyTools) {
                    logger.info("    * {}", tool.getToolDefinition().name());
                }
            }
            
        } catch (Exception e) {
            logger.error("演示第三方MCP管理功能失败", e);
        }
    }

    /**
     * 演示重新加载MCP端点
     *
     * @param chatSession 聊天会话
     */
    public void demonstrateReloadEndpoints(ChatSession chatSession) {
        logger.info("=== 演示重新加载MCP端点 ===");
        
        try {
            // 重新加载第三方MCP工具
            logger.info("重新加载第三方MCP工具...");
            mcpToolRegistrationService.reloadThirdPartyMcpTools(chatSession);
            
            // 显示重新加载后的统计信息
            showToolStatistics(chatSession);
            
        } catch (Exception e) {
            logger.error("重新加载MCP端点失败", e);
        }
        
        logger.info("=== 重新加载演示结束 ===");
    }

    /**
     * 演示错误处理
     *
     * @param chatSession 聊天会话
     */
    public void demonstrateErrorHandling(ChatSession chatSession) {
        logger.info("=== 演示错误处理 ===");
        
        try {
            // 尝试连接到无效的端点
            logger.info("尝试连接到无效的MCP端点...");
            boolean connected = thirdPartyMcpManager.connectToEndpoint(
                "http://invalid-endpoint.example.com/mcp",
                null
            );
            
            if (!connected) {
                logger.info("  - 预期结果: 连接失败，但系统正常处理");
            }
            
            // 检查系统状态
            var stats = mcpToolRegistrationService.getMcpToolStats(chatSession);
            logger.info("  - 系统状态正常: {}", stats.toString());
            
        } catch (Exception e) {
            logger.error("错误处理演示失败", e);
        }
        
        logger.info("=== 错误处理演示结束 ===");
    }

    /**
     * 提供使用建议
     */
    public void provideUsageRecommendations() {
        logger.info("=== MCP工具使用建议 ===");
        
        logger.info("1. 数据库配置:");
        logger.info("   - 在 sys_mcp_endpoint 表中添加MCP端点配置");
        logger.info("   - 设置正确的URL、认证token和HTTP头");
        logger.info("   - 使用 enabled 字段控制端点的启用状态");
        
        logger.info("2. 性能优化:");
        logger.info("   - 调整连接超时时间以适应不同的MCP端点");
        logger.info("   - 监控工具注册的成功率和响应时间");
        logger.info("   - 定期清理无效的MCP端点配置");
        
        logger.info("3. 监控和调试:");
        logger.info("   - 启用DEBUG日志级别查看详细信息");
        logger.info("   - 使用工具统计信息监控系统状态");
        logger.info("   - 定期检查MCP端点的连接状态");
        
        logger.info("4. 安全考虑:");
        logger.info("   - 使用HTTPS端点确保数据传输安全");
        logger.info("   - 定期更新认证token");
        logger.info("   - 限制MCP端点的访问权限");
        
        logger.info("=== 使用建议结束 ===");
    }
}
